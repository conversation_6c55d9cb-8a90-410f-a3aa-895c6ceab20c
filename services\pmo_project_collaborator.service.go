package services

import (
	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectCollaboratorService interface {
	Create(input *PMOCollaboratorCreatePayload) (*models.PMOCollaborator, core.IError)
	Update(id string, input *PMOCollaboratorUpdatePayload) (*models.PMOCollaborator, core.IError)
	Find(id string) (*models.PMOCollaborator, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *PMOCollaboratorPaginationOptions) (*repository.Pagination[models.PMOCollaborator], core.IError)
	Delete(id string) core.IError
}

type pmoProjectCollaboratorService struct {
	ctx core.IContext
}

func (s pmoProjectCollaboratorService) Create(input *PMOCollaboratorCreatePayload) (*models.PMOCollaborator, core.IError) {
	// Check if collaborator already exists for this project and user
	existing, _ := repo.PMOCollaborator(s.ctx).FindOne("project_id = ? AND user_id = ?", input.ProjectID, input.UserID)
	if existing != nil {
		return nil, s.ctx.NewError(nil, emsgs.PMOCollaboratorAlreadyExists)
	}

	collaborator := &models.PMOCollaborator{
		BaseModel:              models.NewBaseModel(),
		ProjectID:              input.ProjectID,
		UserID:                 input.UserID,
		ConfidentialPermission: input.ConfidentialPermission,
		ConfidentialMain:       input.ConfidentialMain,
		SalesPermission:        input.SalesPermission,
		SalesMain:              input.SalesMain,
		PresalesPermission:     input.PresalesPermission,
		PresalesMain:           input.PresalesMain,
		BiddingPermission:      input.BiddingPermission,
		BiddingMain:            input.BiddingMain,
		PMOPermission:          input.PMOPermission,
		PMOMain:                input.PMOMain,
		CreatedByID:            utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID:            utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repo.PMOCollaborator(s.ctx).Create(collaborator)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(collaborator.ID)
}

func (s pmoProjectCollaboratorService) Update(id string, input *PMOCollaboratorUpdatePayload) (*models.PMOCollaborator, core.IError) {
	collaborator, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	updateMap := map[string]interface{}{}
	if input.ConfidentialPermission != nil {
		updateMap["confidential_permission"] = input.ConfidentialPermission
	}
	if input.ConfidentialMain != nil {
		updateMap["confidential_main"] = input.ConfidentialMain
	}
	if input.SalesPermission != nil {
		updateMap["sales_permission"] = input.SalesPermission
	}
	if input.SalesMain != nil {
		updateMap["sales_main"] = input.SalesMain
	}
	if input.PresalesPermission != nil {
		updateMap["presales_permission"] = input.PresalesPermission
	}
	if input.PresalesMain != nil {
		updateMap["presales_main"] = input.PresalesMain
	}
	if input.BiddingPermission != nil {
		updateMap["bidding_permission"] = input.BiddingPermission
	}
	if input.BiddingMain != nil {
		updateMap["bidding_main"] = input.BiddingMain
	}
	if input.PMOPermission != nil {
		updateMap["pmo_permission"] = input.PMOPermission
	}
	if input.PMOMain != nil {
		updateMap["pmo_main"] = input.PMOMain
	}
	updateMap["updated_by_id"] = s.ctx.GetUser().ID

	ierr = repo.PMOCollaborator(s.ctx).Where("id = ?", id).Updates(updateMap)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(collaborator.ID)
}

func (s pmoProjectCollaboratorService) Find(id string) (*models.PMOCollaborator, core.IError) {
	return repo.PMOCollaborator(s.ctx,
		repo.PMOCollaboratorWithProject(),
		repo.PMOCollaboratorWithUser(),
	).FindOne("id = ?", id)
}

func (s pmoProjectCollaboratorService) Pagination(projectID string, pageOptions *core.PageOptions, options *PMOCollaboratorPaginationOptions) (*repository.Pagination[models.PMOCollaborator], core.IError) {
	return repo.PMOCollaborator(s.ctx,
		repo.PMOCollaboratorOrderBy(pageOptions),
		repo.PMOCollaboratorWithProject(),
		repo.PMOCollaboratorWithUser(),
		repo.PMOCollaboratorByProjectID(projectID),
		repo.PMOCollaboratorByTabKey(utils.ToNonPointer(options.TabKey)),
	).Pagination(pageOptions)
}

func (s pmoProjectCollaboratorService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.PMOCollaborator(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewPMOProjectCollaboratorService(ctx core.IContext) IPMOProjectCollaboratorService {
	return &pmoProjectCollaboratorService{ctx: ctx}
}
