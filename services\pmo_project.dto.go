package services

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

type PMOProjectCreatePayload struct {
	Name      string   `json:"name"`
	Slug      string   `json:"slug"`
	Email     string   `json:"email"`
	Tags      []string `json:"tags"`
	ProjectID string   `json:"project_id"`
}

type PMOProjectUpdatePayload struct {
	Name      string                  `json:"name"`
	Slug      string                  `json:"slug"`
	Email     string                  `json:"email"`
	Tags      []string                `json:"tags"`
	Status    models.PMOProjectStatus `json:"status"`
	ProjectID string                  `json:"project_id"`
}

type PMOProjectPaginationOptions struct {
	Status *string `json:"status"`
	TabKey *string `json:"tab_key"`
}

// PMO Document Group DTOs
type PMODocumentGroupCreatePayload struct {
	ProjectID string `json:"project_id"`
	TabKey    string `json:"tab_key"`
	GroupName string `json:"group_name"`
}

type PMODocumentGroupUpdatePayload struct {
	TabKey    string `json:"tab_key"`
	GroupName string `json:"group_name"`
}

type PMODocumentGroupPaginationOptions struct {
	ProjectID *string `json:"project_id"`
	TabKey    *string `json:"tab_key"`
}

// PMO Document Item DTOs
type PMODocumentItemCreatePayload struct {
	ProjectID     string     `json:"project_id"`
	TabKey        string     `json:"tab_key"`
	GroupID       string     `json:"group_id"`
	Name          string     `json:"name"`
	SharepointURL string     `json:"sharepoint_url"`
	Date          *time.Time `json:"date"`
	Type          string     `json:"type"`
	FileID        *string    `json:"file_id"`
}

type PMODocumentItemUpdatePayload struct {
	Name          string     `json:"name"`
	SharepointURL string     `json:"sharepoint_url"`
	Date          *time.Time `json:"date"`
	Type          string     `json:"type"`
	FileID        *string    `json:"file_id"`
}

type PMODocumentItemPaginationOptions struct {
	ProjectID *string `json:"project_id"`
	GroupID   *string `json:"group_id"`
	TabKey    *string `json:"tab_key"`
	Type      *string `json:"type"`
}

// PMO Comment DTOs
type PMOCommentCreatePayload struct {
	ProjectID       string                   `json:"project_id"`
	Channel         models.PMOCommentChannel `json:"channel"`
	Detail          string                   `json:"detail"`
	IsClientFlag    bool                     `json:"is_client_flag"`
	ParentCommentID *string                  `json:"parent_comment_id"`
}

type PMOCommentUpdatePayload struct {
	Detail       string `json:"detail"`
	IsClientFlag bool   `json:"is_client_flag"`
}

type PMOCommentPaginationOptions struct {
	Channel  *string
	ParentID *string `json:"parent_id"`
}

// PMO Collaborator DTOs
type PMOCollaboratorCreatePayload struct {
	ProjectID              string                  `json:"project_id"`
	UserID                 string                  `json:"user_id"`
	ConfidentialPermission models.PMOTabPermission `json:"confidential_permission"`
	ConfidentialMain       bool                    `json:"confidential_main"`
	SalesPermission        models.PMOTabPermission `json:"sales_permission"`
	SalesMain              bool                    `json:"sales_main"`
	PresalesPermission     models.PMOTabPermission `json:"presales_permission"`
	PresalesMain           bool                    `json:"presales_main"`
	BiddingPermission      models.PMOTabPermission `json:"bidding_permission"`
	BiddingMain            bool                    `json:"bidding_main"`
	PMOPermission          models.PMOTabPermission `json:"pmo_permission"`
	PMOMain                bool                    `json:"pmo_main"`
}

type PMOCollaboratorUpdatePayload struct {
	ConfidentialPermission *models.PMOTabPermission `json:"confidential_permission"`
	ConfidentialMain       *bool                    `json:"confidential_main"`
	SalesPermission        *models.PMOTabPermission `json:"sales_permission"`
	SalesMain              *bool                    `json:"sales_main"`
	PresalesPermission     *models.PMOTabPermission `json:"presales_permission"`
	PresalesMain           *bool                    `json:"presales_main"`
	BiddingPermission      *models.PMOTabPermission `json:"bidding_permission"`
	BiddingMain            *bool                    `json:"bidding_main"`
	PMOPermission          *models.PMOTabPermission `json:"pmo_permission"`
	PMOMain                *bool                    `json:"pmo_main"`
}

type PMOCollaboratorPaginationOptions struct {
	TabKey *string `json:"tab_key"`
}
