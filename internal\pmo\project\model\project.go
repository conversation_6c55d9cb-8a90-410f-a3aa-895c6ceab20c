package model

import (
	"github.com/lib/pq"
	"gitlab.finema.co/finema/finework/finework-api/internal/pmo/shared"
	"gitlab.finema.co/finema/finework/finework-api/models"
)

// PMOProject represents a PMO project
type PMOProject struct {
	models.BaseModel
	Name      string                  `json:"name" gorm:"column:name"`
	Slug      string                  `json:"slug" gorm:"column:slug"`
	Email     string                  `json:"email" gorm:"column:email"`
	Tags      pq.StringArray          `json:"tags" gorm:"column:tags;type:text[]"`
	Status    shared.PMOProjectStatus `json:"status" gorm:"column:status"`
	ProjectID string                  `json:"project_id" gorm:"column:project_id;type:uuid"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project              *models.Project          `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Permission           *PMOCollaborator         `json:"permission,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Collaborators        []PMOCollaborator        `json:"collaborators,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Comments             []PMOComment             `json:"comments,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	CommentVersions      []PMOCommentVersion      `json:"comment_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Remarks              []PMORemark              `json:"remarks,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	RemarkVersions       []PMORemarkVersion       `json:"remark_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentGroups       []PMODocumentGroup       `json:"document_groups,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentItems        []PMODocumentItem        `json:"document_items,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentItemVersions []PMODocumentItemVersion `json:"document_item_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Contacts             []PMOContact             `json:"contacts,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Competitors          []PMOCompetitor          `json:"competitors,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Partners             []PMOPartner             `json:"partners,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BudgetInfo           []PMOBudgetInfo          `json:"budget_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BudgetInfoVersions   []PMOBudgetInfoVersion   `json:"budget_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BiddingInfo          []PMOBiddingInfo         `json:"bidding_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BiddingInfoVersions  []PMOBiddingInfoVersion  `json:"bidding_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	ContractInfo         []PMOContractInfo        `json:"contract_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	ContractInfoVersions []PMOContractInfoVersion `json:"contract_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BidbondInfo          []PMOBidbondInfo         `json:"bidbond_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BidbondInfoVersions  []PMOBidbondInfoVersion  `json:"bidbond_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	LGInfo               []PMOLGInfo              `json:"lg_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	LGInfoVersions       []PMOLGInfoVersion       `json:"lg_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	VendorItems          []PMOVendorItem          `json:"vendor_items,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	VendorItemVersions   []PMOVendorItemVersion   `json:"vendor_item_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	CreatedBy            *models.User             `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy            *models.User             `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`
}

func (PMOProject) TableName() string {
	return "pmo_projects"
}

// Forward declarations for related models that will be defined in other files
type PMOCollaborator struct{}
type PMOComment struct{}
type PMOCommentVersion struct{}
type PMODocumentGroup struct{}
type PMODocumentItem struct{}
type PMODocumentItemVersion struct{}
