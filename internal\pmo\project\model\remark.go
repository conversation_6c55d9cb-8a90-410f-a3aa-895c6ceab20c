package model

import (
	"gitlab.finema.co/finema/finework/finework-api/internal/pmo/shared"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMORemark represents project remarks
type PMORemark struct {
	models.BaseModel
	ProjectID string            `json:"project_id" gorm:"column:project_id;type:uuid"`
	TabKey    shared.PMOTabKey  `json:"tab_key" gorm:"column:tab_key"`
	Detail    string            `json:"detail" gorm:"column:detail"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMORemark) TableName() string {
	return "pmo_remarks"
}

// PMORemarkVersion represents versioned project remarks
type PMORemarkVersion struct {
	PMORemark
	OriginalID string `json:"remark_id" gorm:"column:remark_id;type:uuid;index"`
}

func (PMORemarkVersion) TableName() string {
	return "pmo_remark_versions"
}

func (u *PMORemarkVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
