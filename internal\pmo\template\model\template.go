package model

import (
	"gitlab.finema.co/finema/finework/finework-api/internal/pmo/shared"
	"gitlab.finema.co/finema/finework/finework-api/models"
)

// PMOChecklistItem represents checklist items
type PMOChecklistItem struct {
	models.BaseModel
	TabKey    shared.PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	Detail    string           `json:"detail" gorm:"column:detail"`
	IsChecked bool             `json:"is_checked" gorm:"column:is_checked"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`
}

func (PMOChecklistItem) TableName() string {
	return "pmo_checklist_items"
}

// PMOTemplateDocument represents template documents
type PMOTemplateDocument struct {
	models.BaseModel
	TabKey        shared.PM<PERSON>ab<PERSON><PERSON> `json:"tab_key" gorm:"column:tab_key"`
	Name          string           `json:"name" gorm:"column:name"`
	SharepointURL string           `json:"sharepoint_url" gorm:"column:sharepoint_url"`
	CreatedByID   *string          `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID   *string          `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID   *string          `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`
}

func (PMOTemplateDocument) TableName() string {
	return "pmo_template_documents"
}
