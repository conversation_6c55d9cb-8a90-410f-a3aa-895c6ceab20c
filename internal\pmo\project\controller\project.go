package controller

import (
	"net/http"

	"github.com/asaskevich/govalidator"
	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/internal/pmo/project/dto"
	"gitlab.finema.co/finema/finework/finework-api/internal/pmo/project/service"
	"gitlab.finema.co/finema/finework/finework-api/requests"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectController struct {
}

func (m PMOProjectController) Pagination(c core.IHTTPContext) error {
	input := &requests.PMOProjectPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoProjectSvc := service.NewPMOProjectService(c)
	res, ierr := pmoProjectSvc.Pagination(c.GetPageOptions(), &dto.PMOProjectPaginationOptions{
		Status: input.Status,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectController) Find(c core.IHTTPContext) error {
	pmoProjectSvc := service.NewPMOProjectService(c)
	pmoProject, ierr := pmoProjectSvc.Find(c.Param("id"))
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, pmoProject)
}

func (m PMOProjectController) FindBySlug(c core.IHTTPContext) error {
	pmoProjectSvc := service.NewPMOProjectService(c)
	pmoProject, ierr := pmoProjectSvc.FindBySlug(c.Param("slug"))
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, pmoProject)
}

func (m PMOProjectController) Create(c core.IHTTPContext) error {
	input := &requests.PMOProjectCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoProjectSvc := service.NewPMOProjectService(c)
	payload := &dto.PMOProjectCreatePayload{}
	_ = utils.Copy(payload, input)
	pmoProject, err := pmoProjectSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, pmoProject)
}

func (m PMOProjectController) Update(c core.IHTTPContext) error {
	input := &requests.PMOProjectUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoProjectSvc := service.NewPMOProjectService(c)
	payload := &dto.PMOProjectUpdatePayload{}
	_ = utils.Copy(payload, input)
	pmoProject, err := pmoProjectSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, pmoProject)
}

func (m PMOProjectController) Delete(c core.IHTTPContext) error {
	pmoProjectSvc := service.NewPMOProjectService(c)
	ierr := pmoProjectSvc.Delete(c.Param("id"))
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusNoContent, nil)
}

func (m PMOProjectController) CheckSlug(c core.IHTTPContext) error {
	input := &requests.PMOProjectCheckSlugRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if !govalidator.IsSlug(utils.ToNonPointer(input.Slug)) {
		ierr := emsgs.InvalidParamsError
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoProjectSvc := service.NewPMOProjectService(c)
	isAvailable, ierr := pmoProjectSvc.CheckSlug(utils.ToNonPointer(input.Slug))
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"available": isAvailable,
	})
}
