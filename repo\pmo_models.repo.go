package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Collaborator Repository
var PMOCollaborator = repository.Make[models.PMOCollaborator]()

func PMOCollaboratorOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCollaboratorWithProject() repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		c.Preload("Project")
	}
}

func PMOCollaboratorWithUser() repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		c.Preload("User")
		c.Preload("UpdatedBy")
	}
}

func PMOCollaboratorByProjectID(projectID string) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOCollaboratorByTabKey(tabKey string) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if tabKey != "" {
			switch tabKey {
			case "CONFIDENTIAL":
				c.Where("confidential_permission != 'NONE'")
			case "SALES":
				c.Where("sales_permission != 'NONE'")
			case "PRESALES":
				c.Where("presales_permission != 'NONE'")
			case "BIDDING":
				c.Where("bidding_permission != 'NONE'")
			case "PMO":
				c.Where("pmo_permission != 'NONE'")
			}
		}
	}
}

// PMO Comment Repository
var PMOComment = repository.Make[models.PMOComment]()

func PMOCommentOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCommentWithProject() repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		c.Preload("Project")
	}
}

func PMOCommentWithUser() repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		c.Preload("User")
	}
}

func PMOCommentWithVersions() repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		c.Preload("CommentVersions")
	}
}

func PMOCommentByProjectID(projectID string) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOCommentByChannel(channel string) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if channel != "" {
			c.Where("channel = ?", channel)
		}
	}
}

func PMOCommentByParentID(parentID string) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if parentID != "" {
			c.Where("parent_id = ?", parentID)
		}
	}
}

// PMO Comment Version Repository
var PMOCommentVersion = repository.Make[models.PMOCommentVersion]()

func PMOCommentVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCommentVersionByCommentID(commentID string) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if commentID != "" {
			c.Where("comment_id = ?", commentID)
		}
	}
}

func PMOCommentVersionByProjectID(projectID string) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOCommentVersionWithRelations() repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		c.Preload("User")
	}
}

// PMO Budget Info Repository
var PMOBudgetInfo = repository.Make[models.PMOBudgetInfo]()

func PMOBudgetInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBudgetInfo] {
	return func(c repository.IRepository[models.PMOBudgetInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBudgetInfoByProjectID(projectID string) repository.Option[models.PMOBudgetInfo] {
	return func(c repository.IRepository[models.PMOBudgetInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

// PMO Budget Info Version Repository
var PMOBudgetInfoVersion = repository.Make[models.PMOBudgetInfoVersion]()

func PMOBudgetInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBudgetInfoVersionByBudgetInfoID(budgetInfoID string) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if budgetInfoID != "" {
			c.Where("budget_info_id = ?", budgetInfoID)
		}
	}
}

func PMOBudgetInfoVersionByProjectID(projectID string) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

// PMO Bidding Info Repository
var PMOBiddingInfo = repository.Make[models.PMOBiddingInfo]()

func PMOBiddingInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tender_date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBiddingInfoByProjectID(projectID string) repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOBiddingInfoWithVersions() repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		c.Preload("BiddingInfoVersions")
	}
}

// PMO Bidding Info Version Repository
var PMOBiddingInfoVersion = repository.Make[models.PMOBiddingInfoVersion]()

func PMOBiddingInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBiddingInfoVersion] {
	return func(c repository.IRepository[models.PMOBiddingInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBiddingInfoVersionByBiddingInfoID(biddingInfoID string) repository.Option[models.PMOBiddingInfoVersion] {
	return func(c repository.IRepository[models.PMOBiddingInfoVersion]) {
		if biddingInfoID != "" {
			c.Where("bidding_info_id = ?", biddingInfoID)
		}
	}
}

// PMO Contract Info Repository
var PMOContractInfo = repository.Make[models.PMOContractInfo]()

func PMOContractInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("signing_date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOContractInfoByProjectID(projectID string) repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOContractInfoWithVersions() repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		c.Preload("ContractInfoVersions")
	}
}

// PMO Contract Info Version Repository
var PMOContractInfoVersion = repository.Make[models.PMOContractInfoVersion]()

func PMOContractInfoVersionByContractInfoID(contractInfoID string) repository.Option[models.PMOContractInfoVersion] {
	return func(c repository.IRepository[models.PMOContractInfoVersion]) {
		if contractInfoID != "" {
			c.Where("contract_info_id = ?", contractInfoID)
		}
	}
}

// PMO Document Group Repository
var PMODocumentGroup = repository.Make[models.PMODocumentGroup]()

func PMODocumentGroupOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tab_key ASC, group_name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMODocumentGroupWithProject() repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		c.Preload("Project")
	}
}

func PMODocumentGroupWithDocumentItems() repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		c.Preload("DocumentItems")
	}
}

func PMODocumentGroupByProjectID(projectID string) repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMODocumentGroupByTabKey(tabKey string) repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		if tabKey != "" {
			c.Where("tab_key = ?", tabKey)
		}
	}
}

func PMODocumentGroupWithSearch(q string) repository.Option[models.PMODocumentGroup] {
	return func(c repository.IRepository[models.PMODocumentGroup]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("group_name ILIKE ?", searchTerm)
	}
}

// PMO Document Item Repository
var PMODocumentItem = repository.Make[models.PMODocumentItem]()

func PMODocumentItemOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tab_key ASC, name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMODocumentItemWithProject() repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		c.Preload("Project")
	}
}

func PMODocumentItemWithGroup() repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		c.Preload("Group")
	}
}

func PMODocumentItemWithFile() repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		c.Preload("File")
	}
}

func PMODocumentItemByProjectID(projectID string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMODocumentItemByGroupID(groupID string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if groupID != "" {
			c.Where("group_id = ?", groupID)
		}
	}
}

func PMODocumentItemByTabKey(tabKey string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if tabKey != "" {
			c.Where("tab_key = ?", tabKey)
		}
	}
}

func PMODocumentItemByType(docType string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if docType != "" {
			c.Where("type = ?", docType)
		}
	}
}

func PMODocumentItemWithSearch(q string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ? OR sharepoint_url ILIKE ?", searchTerm, searchTerm)
	}
}

// PMO Document Item Version Repository
var PMODocumentItemVersion = repository.Make[models.PMODocumentItemVersion]()

func PMODocumentItemVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMODocumentItemVersion] {
	return func(c repository.IRepository[models.PMODocumentItemVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMODocumentItemVersionByOriginalID(originalID string) repository.Option[models.PMODocumentItemVersion] {
	return func(c repository.IRepository[models.PMODocumentItemVersion]) {
		if originalID != "" {
			c.Where("document_item_id = ?", originalID)
		}
	}
}
