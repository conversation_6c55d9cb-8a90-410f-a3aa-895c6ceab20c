package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMODocumentGroupUpdate struct {
	core.BaseValidator
	TabKey    *string `json:"tab_key"`
	GroupName *string `json:"group_name"`
}

func (r *PMODocumentGroupUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.<PERSON>b<PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.Error()
}
