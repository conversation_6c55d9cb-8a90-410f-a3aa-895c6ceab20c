package requests

import (
	"strings"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMODocumentItemUpdate struct {
	core.BaseValidator
	Name          *string    `json:"name"`
	SharepointURL *string    `json:"sharepoint_url"`
	Date          *time.Time `json:"date"`
	Type          *string    `json:"type"`
	FileID        *string    `json:"file_id"`
}

func (r *PMODocumentItemUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Type, strings.Join(models.PMODocTypes, "|"), "type"))
	r.Must(r.IsURL(r.SharepointURL, "sharepoint_url"))

	r.Must(r.IsExists(ctx, r.FileID, models.File{}.TableName(), "id", "file_id"))

	return r.Error()
}
