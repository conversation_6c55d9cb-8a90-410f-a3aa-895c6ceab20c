package cmd

import (
	"fmt"
	"os"

	"gitlab.finema.co/finema/finework/finework-api/modules/auth"
	"gitlab.finema.co/finema/finework/finework-api/modules/checkin"
	"gitlab.finema.co/finema/finework/finework-api/modules/department"
	"gitlab.finema.co/finema/finework/finework-api/modules/holiday"
	"gitlab.finema.co/finema/finework/finework-api/modules/home"
	"gitlab.finema.co/finema/finework/finework-api/modules/me"
	"gitlab.finema.co/finema/finework/finework-api/modules/ministry"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo_project"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo_template"
	"gitlab.finema.co/finema/finework/finework-api/modules/project"
	"gitlab.finema.co/finema/finework/finework-api/modules/sga"
	"gitlab.finema.co/finema/finework/finework-api/modules/team"
	"gitlab.finema.co/finema/finework/finework-api/modules/timesheet"
	"gitlab.finema.co/finema/finework/finework-api/modules/upload"
	"gitlab.finema.co/finema/finework/finework-api/modules/user"
	core "gitlab.finema.co/finema/idin-core"
)

func APIRun() {
	os.Setenv("TZ", "Asia/Bangkok")

	env := core.NewEnv()
	db, err := core.NewDatabase(env.Config()).Connect()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Postgres: %v \n", err)
		os.Exit(1)
	}

	e := core.NewHTTPServer(&core.HTTPContextOptions{
		ContextOptions: &core.ContextOptions{
			DB:  db,
			ENV: env,
		},
	})

	//e.Pre(middleware.Rewrite(map[string]string{
	//	"/api/*": "/$1",
	//}))

	home.NewHomeHTTP(e)

	// Register modules
	auth.NewAuthHTTP(e)
	me.NewMeHTTP(e)
	user.NewUserHTTP(e)
	holiday.NewHolidayHTTP(e)
	team.NewTeamHTTP(e)
	sga.NewSgaHTTP(e)
	project.NewProjectHTTP(e)
	timesheet.NewTimesheetHTTP(e)
	checkin.NewCheckinHTTP(e)
	ministry.NewMinistryHTTP(e)
	department.NewDepartmentHTTP(e)
	pmo_project.NewPMOProjectHTTP(e)
	pmo_template.NewPMOTemplateHTTP(e)
	upload.NewUploadHTTP(e)
	core.StartHTTPServer(e, env)
}
