package pmo_template

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOTemplateController struct {
}

// PMO Template Documents methods
func (m PMOTemplateController) DocumentsPagination(c core.IHTTPContext) error {
	input := &requests.PMOTemplateDocumentPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoTemplateSvc := services.NewPMOTemplateService(c)
	res, ierr := pmoTemplateSvc.DocumentsPagination(c.GetPageOptions(), &services.PMOTemplateDocumentPaginationOptions{
		TabKey: input.TabKey,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOTemplateController) DocumentsFind(c core.IHTTPContext) error {
	pmoTemplateSvc := services.NewPMOTemplateService(c)
	document, err := pmoTemplateSvc.DocumentsFind(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, document)
}

func (m PMOTemplateController) DocumentsCreate(c core.IHTTPContext) error {
	input := &requests.PMOTemplateDocumentCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoTemplateSvc := services.NewPMOTemplateService(c)
	payload := &services.PMOTemplateDocumentCreatePayload{}
	_ = utils.Copy(payload, input)
	document, err := pmoTemplateSvc.DocumentsCreate(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, document)
}

func (m PMOTemplateController) DocumentsUpdate(c core.IHTTPContext) error {
	input := &requests.PMOTemplateDocumentUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoTemplateSvc := services.NewPMOTemplateService(c)
	payload := &services.PMOTemplateDocumentUpdatePayload{}
	_ = utils.Copy(payload, input)
	document, err := pmoTemplateSvc.DocumentsUpdate(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, document)
}

func (m PMOTemplateController) DocumentsDelete(c core.IHTTPContext) error {
	pmoTemplateSvc := services.NewPMOTemplateService(c)
	err := pmoTemplateSvc.DocumentsDelete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

// PMO Template Checklist Items methods
func (m PMOTemplateController) ChecklistItemsPagination(c core.IHTTPContext) error {
	input := &requests.PMOTemplateChecklistItemPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoTemplateSvc := services.NewPMOTemplateService(c)
	res, ierr := pmoTemplateSvc.ChecklistItemsPagination(c.GetPageOptions(), &services.PMOTemplateChecklistItemPaginationOptions{
		TabKey: input.TabKey,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOTemplateController) ChecklistItemsFind(c core.IHTTPContext) error {
	pmoTemplateSvc := services.NewPMOTemplateService(c)
	item, err := pmoTemplateSvc.ChecklistItemsFind(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, item)
}

func (m PMOTemplateController) ChecklistItemsCreate(c core.IHTTPContext) error {
	input := &requests.PMOTemplateChecklistItemCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoTemplateSvc := services.NewPMOTemplateService(c)
	payload := &services.PMOTemplateChecklistItemCreatePayload{}
	_ = utils.Copy(payload, input)
	item, err := pmoTemplateSvc.ChecklistItemsCreate(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, item)
}

func (m PMOTemplateController) ChecklistItemsUpdate(c core.IHTTPContext) error {
	input := &requests.PMOTemplateChecklistItemUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoTemplateSvc := services.NewPMOTemplateService(c)
	payload := &services.PMOTemplateChecklistItemUpdatePayload{}
	_ = utils.Copy(payload, input)
	item, err := pmoTemplateSvc.ChecklistItemsUpdate(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, item)
}

func (m PMOTemplateController) ChecklistItemsDelete(c core.IHTTPContext) error {
	pmoTemplateSvc := services.NewPMOTemplateService(c)
	err := pmoTemplateSvc.ChecklistItemsDelete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
