package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOCommentCreate struct {
	core.BaseValidator
	Channel         *string `json:"channel"`
	Detail          *string `json:"detail"`
	IsClientFlag    *bool   `json:"is_client_flag"`
	ParentCommentID *string `json:"parent_comment_id"`
}

func (r *PMOCommentCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Channel, "channel"))
	r.Must(r.IsStrRequired(r.Detail, "detail"))

	r.Must(r.IsStrIn(r.Channel, strings.Join(models.PMOCommentChannels, "|"), "channel"))

	r.Must(r.IsExists(ctx, r.ParentCommentID, models.PMOComment{}.TableName(), "id", "parent_comment_id"))

	return r.<PERSON>rror()
}
