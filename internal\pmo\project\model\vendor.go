package model

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMOVendorItem represents project vendor items
type PMOVendorItem struct {
	models.BaseModel
	ProjectID          string `json:"project_id" gorm:"column:project_id;type:uuid"`
	VendorName         string `json:"vendor_name" gorm:"column:vendor_name"`
	ItemName           string `json:"item_name" gorm:"column:item_name"`
	ItemDetail         string `json:"item_detail" gorm:"column:item_detail"`
	DeliverDurationDay int    `json:"deliver_duration_day" gorm:"column:deliver_duration_day"`
	IsTor              bool   `json:"is_tor" gorm:"column:is_tor"`
	IsImplementation   bool   `json:"is_implementation" gorm:"column:is_implementation"`
	IsTraining         bool   `json:"is_training" gorm:"column:is_training"`
	IsUserManual       bool   `json:"is_user_manual" gorm:"column:is_user_manual"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOVendorItem) TableName() string {
	return "pmo_vendor_items"
}

// PMOVendorItemVersion represents versioned vendor items
type PMOVendorItemVersion struct {
	PMOVendorItem
	OriginalID string `json:"vendor_item_id" gorm:"column:vendor_item_id;type:uuid;index"`
}

func (PMOVendorItemVersion) TableName() string {
	return "pmo_vendor_items_versions"
}

func (u *PMOVendorItemVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
