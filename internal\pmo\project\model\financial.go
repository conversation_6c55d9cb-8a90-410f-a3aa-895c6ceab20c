package model

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMOBudgetInfo represents project budget information
type PMOBudgetInfo struct {
	models.BaseModel
	ProjectID    string  `json:"project_id" gorm:"column:project_id;type:uuid"`
	FundType     string  `json:"fund_type" gorm:"column:fund_type"`
	ProjectValue float64 `json:"project_value" gorm:"column:project_value"`
	BidbondValue float64 `json:"bidbond_value" gorm:"column:bidbond_value"`
	Partner      string  `json:"partner" gorm:"column:partner"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOBudgetInfo) TableName() string {
	return "pmo_budget_info"
}

// PMOBudgetInfoVersion represents versioned budget information
type PMOBudgetInfoVersion struct {
	PMOBudgetInfo
	OriginalID string `json:"budget_info_id" gorm:"column:budget_info_id;type:uuid;index"`
}

func (PMOBudgetInfoVersion) TableName() string {
	return "pmo_budget_info_versions"
}

func (u *PMOBudgetInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}

// PMOBiddingInfo represents project bidding information
type PMOBiddingInfo struct {
	models.BaseModel
	ProjectID    string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	BiddingType  string     `json:"bidding_type" gorm:"column:bidding_type"`
	BiddingValue float64    `json:"bidding_value" gorm:"column:bidding_value"`
	TenderDate   *time.Time `json:"tender_date" gorm:"column:tender_date;type:date"`
	TenderEntity string     `json:"tender_entity" gorm:"column:tender_entity"`
	AnnounceDate *time.Time `json:"announce_date" gorm:"column:announce_date;type:date"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOBiddingInfo) TableName() string {
	return "pmo_bidding_info"
}

// PMOBiddingInfoVersion represents versioned bidding information
type PMOBiddingInfoVersion struct {
	PMOBiddingInfo
	OriginalID string `json:"bidding_info_id" gorm:"column:bidding_info_id;type:uuid;index"`
}

func (PMOBiddingInfoVersion) TableName() string {
	return "pmo_bidding_info_versions"
}

func (u *PMOBiddingInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}

// PMOContractInfo represents project contract information
type PMOContractInfo struct {
	models.BaseModel
	ProjectID            string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	ContractNo           string     `json:"contract_no" gorm:"column:contract_no"`
	Value                float64    `json:"value" gorm:"column:value"`
	SigningDate          *time.Time `json:"signing_date" gorm:"column:signing_date;type:date"`
	StartDate            *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate              *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	DurationDay          int        `json:"duration_day" gorm:"column:duration_day"`
	WarrantyDurationDay  int        `json:"warranty_duration_day" gorm:"column:warranty_duration_day"`
	WarrantyDurationYear int        `json:"warranty_duration_year" gorm:"column:warranty_duration_year"`
	Prime                string     `json:"prime" gorm:"column:prime"`
	PenaltyFee           float64    `json:"penalty_fee" gorm:"column:penalty_fee"`
	IsLegalizeStamp      bool       `json:"is_legalize_stamp" gorm:"column:is_legalize_stamp"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOContractInfo) TableName() string {
	return "pmo_contract_info"
}

// PMOContractInfoVersion represents versioned contract information
type PMOContractInfoVersion struct {
	PMOContractInfo
	OriginalID string `json:"contract_info_id" gorm:"column:contract_info_id;type:uuid;index"`
}

func (PMOContractInfoVersion) TableName() string {
	return "pmo_contract_info_versions"
}

func (u *PMOContractInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}

// PMOBidbondInfo represents project bidbond information
type PMOBidbondInfo struct {
	models.BaseModel
	ProjectID      string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	GuaranteeAsset string     `json:"guarantee_asset" gorm:"column:guarantee_asset"`
	BidbondPayer   string     `json:"bidbond_payer" gorm:"column:bidbond_payer"`
	BidbondValue   float64    `json:"bidbond_value" gorm:"column:bidbond_value"`
	StartDate      *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate        *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	DurationMonth  int        `json:"duration_month" gorm:"column:duration_month"`
	DurationYear   int        `json:"duration_year" gorm:"column:duration_year"`
	Fee            float64    `json:"fee" gorm:"column:fee"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOBidbondInfo) TableName() string {
	return "pmo_bidbond_info"
}

// PMOBidbondInfoVersion represents versioned bidbond information
type PMOBidbondInfoVersion struct {
	PMOBidbondInfo
	OriginalID string `json:"bidbond_info_id" gorm:"column:bidbond_info_id;type:uuid;index"`
}

func (PMOBidbondInfoVersion) TableName() string {
	return "pmo_bidbond_info_versions"
}

func (u *PMOBidbondInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}

// PMOLGInfo represents project letter of guarantee information
type PMOLGInfo struct {
	models.BaseModel
	ProjectID string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	Value     float64    `json:"value" gorm:"column:value"`
	StartDate *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate   *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	Fee       float64    `json:"fee" gorm:"column:fee"`
	Interest  float64    `json:"interest" gorm:"column:interest"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOLGInfo) TableName() string {
	return "pmo_lg_info"
}

// PMOLGInfoVersion represents versioned LG information
type PMOLGInfoVersion struct {
	PMOLGInfo
	OriginalID string `json:"lg_info_id" gorm:"column:lg_info_id;type:uuid;index"`
}

func (PMOLGInfoVersion) TableName() string {
	return "pmo_lg_info_versions"
}

func (u *PMOLGInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
