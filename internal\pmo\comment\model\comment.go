package model

import (
	"gitlab.finema.co/finema/finework/finework-api/internal/pmo/shared"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMOComment represents project comments
type PMOComment struct {
	models.BaseModel
	ProjectID       string                    `json:"project_id" gorm:"column:project_id;type:uuid"`
	UserID          string                    `json:"user_id" gorm:"column:user_id;type:uuid"`
	Channel         shared.PMOCommentChannel `json:"channel" gorm:"column:channel"`
	Detail          string                    `json:"detail" gorm:"column:detail"`
	IsClientFlag    bool                      `json:"is_client_flag" gorm:"column:is_client_flag"`
	ParentCommentID *string                   `json:"parent_comment_id" gorm:"column:parent_comment_id;type:uuid"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject  `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	User    *models.User `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
}

func (PMOComment) TableName() string {
	return "pmo_comments"
}

// PMOCommentVersion represents versioned project comments
type PMOCommentVersion struct {
	PMOComment
	OriginalID string `json:"comment_id" gorm:"column:comment_id;type:uuid;index"`
}

func (PMOCommentVersion) TableName() string {
	return "pmo_comment_versions"
}

func (u *PMOCommentVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}

// Forward declaration for PMOProject
type PMOProject struct{}
