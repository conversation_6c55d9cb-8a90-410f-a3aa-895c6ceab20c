package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOProjectUpdate struct {
	core.BaseValidator
	Name      *string  `json:"name"`
	Slug      *string  `json:"slug"`
	Email     *string  `json:"email"`
	Tags      []string `json:"tags"`
	Status    *string  `json:"status"`
	ProjectID *string  `json:"project_id"`
}

func (r *PMOProjectUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>mail(r.Email, "email"))

	// Check uniqueness only if values are provided
	if r.Name != nil {
		cc := ctx.(core.IHTTPContext)
		pmoProject, _ := repo.PMOProject(cc).FindOne("id = ?", cc.Param("id"))
		if pmoProject != nil {
			r.Must(r.<PERSON>que(ctx, r.Name, models.PMOProject{}.TableName(), "name", pmoProject.ID, "name"))
		}
	}

	if r.Slug != nil {
		cc := ctx.(core.IHTTPContext)
		pmoProject, _ := repo.PMOProject(cc).FindOne("id = ?", cc.Param("id"))
		if pmoProject != nil {
			r.Must(r.IsStrUnique(ctx, r.Slug, models.PMOProject{}.TableName(), "slug", pmoProject.ID, "slug"))
		}
	}

	r.Must(r.IsExists(ctx, r.ProjectID, models.Project{}.TableName(), "id", "project_id"))
	r.Must(r.IsStrIn(r.Status, strings.Join(models.PMOProjectStatuses, "|"), "status"))

	return r.Error()
}
