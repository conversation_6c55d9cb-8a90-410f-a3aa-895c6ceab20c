package service

import (
	"gitlab.finema.co/finema/finework/finework-api/internal/pmo/project/dto"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	coreRepo "gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectService interface {
	Create(input *dto.PMOProjectCreatePayload) (*models.PMOProject, core.IError)
	Update(id string, input *dto.PMOProjectUpdatePayload) (*models.PMOProject, core.IError)
	Find(id string) (*models.PMOProject, core.IError)
	FindBySlug(slug string) (*models.PMOProject, core.IError)
	Pagination(pageOptions *core.PageOptions, options *dto.PMOProjectPaginationOptions) (*coreRepo.Pagination[models.PMOProject], core.IError)
	Delete(id string) core.IError
	CheckSlug(slug string) (bool, core.IError)
}

type pmoProjectService struct {
	ctx core.IContext
}

func NewPMOProjectService(ctx core.IContext) IPMOProjectService {
	return &pmoProjectService{ctx: ctx}
}

func (s pmoProjectService) Create(input *dto.PMOProjectCreatePayload) (*models.PMOProject, core.IError) {
	pmoProject := &models.PMOProject{
		BaseModel:   models.NewBaseModel(),
		Name:        input.Name,
		Slug:        input.Slug,
		Email:       input.Email,
		Tags:        input.Tags,
		Status:      models.PMOProjectStatusDraft,
		ProjectID:   input.ProjectID,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repo.PMOProject(s.ctx).Create(pmoProject)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(pmoProject.ID)
}

func (s pmoProjectService) Update(id string, input *dto.PMOProjectUpdatePayload) (*models.PMOProject, core.IError) {
	pmoProject, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	pmoProject.Name = input.Name
	pmoProject.Slug = input.Slug
	pmoProject.Email = input.Email
	pmoProject.Tags = input.Tags
	pmoProject.Status = input.Status
	pmoProject.ProjectID = input.ProjectID
	pmoProject.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repo.PMOProject(s.ctx).Update(pmoProject.ID, pmoProject)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(pmoProject.ID)
}

func (s pmoProjectService) Find(id string) (*models.PMOProject, core.IError) {
	pmoProject, ierr := repo.PMOProject(s.ctx).FindByID(id, repo.PMOProjectWithProject())
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return pmoProject, nil
}

func (s pmoProjectService) FindBySlug(slug string) (*models.PMOProject, core.IError) {
	pmoProject, ierr := repo.PMOProject(s.ctx).FindOne(repo.PMOProjectBySlug(slug), repo.PMOProjectWithProject())
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return pmoProject, nil
}

func (s pmoProjectService) Pagination(pageOptions *core.PageOptions, options *dto.PMOProjectPaginationOptions) (*coreRepo.Pagination[models.PMOProject], core.IError) {
	res, ierr := repo.PMOProject(s.ctx).Paginate(
		pageOptions,
		repo.PMOProjectOrderBy(pageOptions),
		repo.PMOProjectWithStatus(utils.ToNonPointer(options.Status)),
		repo.PMOProjectWithProject(),
	)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return res, nil
}

func (s pmoProjectService) Delete(id string) core.IError {
	pmoProject, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	pmoProject.DeletedByID = utils.ToPointer(s.ctx.GetUser().ID)
	ierr = repo.PMOProject(s.ctx).SoftDelete(pmoProject)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return nil
}

func (s pmoProjectService) CheckSlug(slug string) (bool, core.IError) {
	_, ierr := repo.PMOProject(s.ctx).FindOne(repo.PMOProjectBySlug(&slug))
	if ierr != nil {
		if ierr.GetCode() == "RECORD_NOT_FOUND" {
			return true, nil
		}
		return false, s.ctx.NewError(ierr, ierr)
	}

	return false, nil
}
