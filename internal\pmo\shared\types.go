package shared

// PMOProjectStatus represents the status of a PMO project
type PMOProjectStatus string

const (
	PMOProjectStatusDraft    PMOProjectStatus = "DRAFT"
	PMOProjectStatusTor      PMOProjectStatus = "TOR"
	PMOProjectStatusBidding  PMOProjectStatus = "BIDDING"
	PMOProjectStatusPMO      PMOProjectStatus = "PMO"
	PMOProjectStatusWarranty PMOProjectStatus = "WARRANTY"
	PMOProjectStatusClosed   PMOProjectStatus = "CLOSED"
	PMOProjectStatusCancel   PMOProjectStatus = "CANCEL"
)

var PMOProjectStatuses = []string{
	string(PMOProjectStatusDraft),
	string(PMOProjectStatusTor),
	string(PMOProjectStatusBidding),
	string(PMOProjectStatusPMO),
	string(PMOProjectStatusWarranty),
	string(PMOProjectStatusClosed),
	string(PMOProjectStatusCancel),
}

// PMOTabPermission represents permission levels for PMO tabs
type PMOTabPermission string

const (
	PMOTabPermissionReadonly PMOTabPermission = "READONLY"
	PMOTabPermissionModify   PMOTabPermission = "MODIFY"
	PMOTabPermissionNone     PMOTabPermission = "NONE"
)

var PMOTabPermissions = []string{
	string(PMOTabPermissionReadonly),
	string(PMOTabPermissionModify),
	string(PMOTabPermissionNone),
}

// PMOCommentChannel represents different comment channels
type PMOCommentChannel string

const (
	PMOCommentChannelOverall      PMOCommentChannel = "OVERALL"
	PMOCommentChannelConfidential PMOCommentChannel = "CONFIDENTIAL"
	PMOCommentChannelSales        PMOCommentChannel = "SALES"
	PMOCommentChannelPresales     PMOCommentChannel = "PRESALES"
	PMOCommentChannelBidding      PMOCommentChannel = "BIDDING"
	PMOCommentChannelPMOProgress  PMOCommentChannel = "PMO_PROGRESS"
	PMOCommentChannelPMOHWSW      PMOCommentChannel = "PMO_HWSW"
	PMOCommentChannelPMOWarranty  PMOCommentChannel = "PMO_WARRANTY"
	PMOCommentChannelPMOLegal     PMOCommentChannel = "PMO_LEGAL"
	PMOCommentChannelBizco        PMOCommentChannel = "BIZCO"
)

var PMOCommentChannels = []string{
	string(PMOCommentChannelOverall),
	string(PMOCommentChannelConfidential),
	string(PMOCommentChannelSales),
	string(PMOCommentChannelPresales),
	string(PMOCommentChannelBidding),
	string(PMOCommentChannelPMOProgress),
	string(PMOCommentChannelPMOHWSW),
	string(PMOCommentChannelPMOWarranty),
	string(PMOCommentChannelPMOLegal),
	string(PMOCommentChannelBizco),
}

// PMODocType represents document types
type PMODocType string

const (
	PMODocTypeInbound  PMODocType = "INBOUND"
	PMODocTypeOutbound PMODocType = "OUTBOUND"
)

var PMODocTypes = []string{
	string(PMODocTypeInbound),
	string(PMODocTypeOutbound),
}

// PMOTabKey represents different PMO tabs
type PMOTabKey string

const (
	PMOTabKeyInfo         PMOTabKey = "INFO"
	PMOTabKeyConfidential PMOTabKey = "CONFIDENTIAL"
	PMOTabKeySales        PMOTabKey = "SALES"
	PMOTabKeyPresales     PMOTabKey = "PRESALES"
	PMOTabKeyBidding      PMOTabKey = "BIDDING"
	PMOTabKeyPMO          PMOTabKey = "PMO"
	PMOTabKeyBizco        PMOTabKey = "BIZCO"
)

var PMOTabKeys = []string{
	string(PMOTabKeyInfo),
	string(PMOTabKeyConfidential),
	string(PMOTabKeySales),
	string(PMOTabKeyPresales),
	string(PMOTabKeyBidding),
	string(PMOTabKeyPMO),
	string(PMOTabKeyBizco),
}
